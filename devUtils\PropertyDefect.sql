SELECT "defect"."Id"
      ,"defect"."PropertyId"
      ,"property"."Address" "__Property"
      ,"defect"."DefectCategoryCode"
      ,"defect"."Description"
      ,"defect"."AreaCode"
      ,"area"."Name" "__Area"
      ,"defect"."LocationCode"
      ,"location"."Name" "__Location"
      ,"defect"."OrientationCode"
      ,"orientation"."Name" "__Orientation"
      ,"defect"."Defects"
      ,"defect"."ServerityCode"
      ,"serverity"."Name" "__Serverity"
      ,"defect"."CreatedAt"
      ,"defect"."ModifiedAt"
      ,"defect"."Deleted"
FROM public."PropertyDefect" "defect"
INNER JOIN public."Property" "property" ON "defect"."PropertyId" = "property"."Id"
LEFT JOIN public."Area" "area" ON "defect"."AreaCode" = "area"."Code"
LEFT JOIN public."Location" "location" ON "defect"."LocationCode" = "location"."Code"
LEFT JOIN public."Orientation" "orientation" ON "defect"."OrientationCode" = "orientation"."Code"
LEFT JOIN public."Serverity" "serverity" ON "defect"."ServerityCode" = "serverity"."Code"
WHERE 1=1
      AND "defect"."Deleted" = 'False'
ORDER BY "defect"."CreatedAt" DESC;
