
-- ==========================================
-- TRUNCATE ALL TABLES IN CORRECT ORDER
-- ==========================================
-- This script truncates all tables in the correct order to avoid foreign key constraint violations.
-- Tables with foreign key dependencies must be truncated before the tables they reference.

-- Level 3 tables (most dependent)
TRUNCATE TABLE public."DefectImage" CASCADE;

-- Level 2 tables
TRUNCATE TABLE public."PropertyDefect" CASCADE;
TRUNCATE TABLE public."Report" CASCADE;

-- Level 1 tables
TRUNCATE TABLE public."Property" CASCADE;
TRUNCATE TABLE public."Contact" CASCADE;
TRUNCATE TABLE public."UserPersistedData" CASCADE;

-- Level 0 tables (independent) - Option tables
TRUNCATE TABLE public."O_Weather" CASCADE;
TRUNCATE TABLE public."O_Walls" CASCADE;
TRUNCATE TABLE public."O_Roof" CASCADE;
TRUNCATE TABLE public."O_OtherTimberBldgElements" CASCADE;
TRUNCATE TABLE public."O_OtherBuildingElements" CASCADE;
TRUNCATE TABLE public."O_Floor" CASCADE;
TRUNCATE TABLE public."O_Occupied" CASCADE;
TRUNCATE TABLE public."O_Furnished" CASCADE;
TRUNCATE TABLE public."O_Storeys" CASCADE;
TRUNCATE TABLE public."O_Orientation" CASCADE;
TRUNCATE TABLE public."O_NumBedrooms" CASCADE;
TRUNCATE TABLE public."O_CompanyStrataTitle" CASCADE;
TRUNCATE TABLE public."O_BuildingType" CASCADE;

-- Level 0 tables (independent) - Core tables
TRUNCATE TABLE public."GlobalSettings" CASCADE;
TRUNCATE TABLE public."User" CASCADE;