import { sql } from "@vercel/postgres";
import { PropertyDefectModel } from "@/app/models/PropertyDefect";

export class PropertyDefectBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async Get(id: string) {
        return (await sql<PropertyDefectModel>`
            SELECT "Id",
                   "PropertyId",
                   "DefectCategoryCode",
                   "Description",
                   "AreaCode",
                   "LocationCode",
                   "OrientationCode",
                   "Defects",
                   "ServerityCode",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."PropertyDefect"
            WHERE "Id" = ${id}
              AND "Deleted" = 'False'
        `).rows[0];
    }

    static async GetList() {
        return (await sql<PropertyDefectModel>`
            SELECT "Id",
                   "PropertyId",
                   "DefectCategoryCode",
                   "Description",
                   "AreaCode",
                   "LocationCode",
                   "OrientationCode",
                   "Defects",
                   "ServerityCode",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."PropertyDefect"
            WHERE "Deleted" = 'False'
            ORDER BY "CreatedAt" DESC
        `).rows;
    }

    static async GetByPropertyId(propertyId: string) {
        return (await sql<PropertyDefectModel>`
            SELECT "Id",
                   "PropertyId",
                   "DefectCategoryCode",
                   "Description",
                   "AreaCode",
                   "LocationCode",
                   "OrientationCode",
                   "Defects",
                   "ServerityCode",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."PropertyDefect"
            WHERE "PropertyId" = ${propertyId}
              AND "Deleted" = 'False'
            ORDER BY "CreatedAt" DESC
        `).rows;
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(data: PropertyDefectModel) {
        await sql`
            INSERT INTO public."PropertyDefect"
            (
                "Id",
                "PropertyId",
                "DefectCategoryCode",
                "Description",
                "AreaCode",
                "LocationCode",
                "OrientationCode",
                "Defects",
                "ServerityCode",
                "CreatedAt"
            )
            VALUES
            (
                ${data.Id},
                ${data.PropertyId},
                ${data.DefectCategoryCode},
                ${data.Description},
                ${data.AreaCode},
                ${data.LocationCode},
                ${data.OrientationCode},
                ${data.Defects},
                ${data.ServerityCode},
                ${new Date().toUTCString()}
            )
        `;
    }

    static async CreateList(dataList: PropertyDefectModel[]) {
        for (const data of dataList) {
            await this.Create(data);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(data: PropertyDefectModel) {
        await sql`
            UPDATE public."PropertyDefect"
            SET "PropertyId" = ${data.PropertyId},
                "DefectCategoryCode" = ${data.DefectCategoryCode},
                "Description" = ${data.Description},
                "AreaCode" = ${data.AreaCode},
                "LocationCode" = ${data.LocationCode},
                "OrientationCode" = ${data.OrientationCode},
                "Defects" = ${data.Defects},
                "ServerityCode" = ${data.ServerityCode},
                "ModifiedAt" = ${new Date().toUTCString()},
                "Deleted" = ${data.Deleted}
            WHERE "Id" = ${data.Id}
        `;
    }

    static async UpdateList(dataList: PropertyDefectModel[]) {
        for (const data of dataList) {
            await this.Update(data);
        }
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id: string) {
        await sql`
            UPDATE public."PropertyDefect"
            SET "Deleted" = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

    static async DeleteList(ids: string[]) {
        for (const id of ids) {
            await this.Delete(id);
        }
    }
}
