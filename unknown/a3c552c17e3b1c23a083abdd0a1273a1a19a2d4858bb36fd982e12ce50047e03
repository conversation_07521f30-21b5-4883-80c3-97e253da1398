import puppeteer from 'puppeteer';

export async function generatePdfFromHtml(
    html: string,
    options?: {
        format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal';
        margin?: { top?: string; right?: string; bottom?: string; left?: string; };
        printBackground?: boolean;
        landscape?: boolean;
    }
): Promise<Buffer> {
    let browser;
    try {
        // Launch browser
        browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage();

        // Set content
        await page.setContent(html, {
            waitUntil: 'networkidle0'
        });

        // Generate PDF
        const pdfBuffer = await page.pdf({
            format: options?.format || 'A4',
            margin: options?.margin || { top: '1cm', right: '1cm', bottom: '1cm', left: '1cm' },
            printBackground: options?.printBackground !== false,
            landscape: options?.landscape || false
        });

        return Buffer.from(pdfBuffer);
    } catch (error) {
        console.error('Error generating PDF from HTML:', error);
        throw error;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}
