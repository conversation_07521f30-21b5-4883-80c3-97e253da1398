SELECT "report"."Id"
      ,"report"."PropertyId"
      ,"property"."Address" "__Property"
      ,"report"."UserId"
      ,CONCAT("user"."FirstName", ' ', "user"."LastName") "__User"
      ,"user"."Email" "__UserEmail"
      ,"report"."Name"
      ,"report"."PostalAddress"
      ,"report"."ClientName"
      ,"report"."ClientPhone"
      ,"report"."ClientEmail"
      ,"report"."ClientPrincipalName"
      ,"report"."InspectionDate"
      ,"report"."InspectorNameOverride"
      ,"report"."InspectorPhoneOverride"
      ,"report"."InspectorQualificationOverride"
      ,"report"."FileId"
      ,"report"."CreatedAt"
      ,"report"."ModifiedAt"
      ,"report"."Deleted"
FROM public."Report" "report"
INNER JOIN public."Property" "property" ON "report"."PropertyId" = "property"."Id"
INNER JOIN public."User" "user" ON "report"."UserId" = "user"."Id"
WHERE 1=1
      AND "report"."Deleted" = 'False'
ORDER BY "report"."CreatedAt" DESC;
