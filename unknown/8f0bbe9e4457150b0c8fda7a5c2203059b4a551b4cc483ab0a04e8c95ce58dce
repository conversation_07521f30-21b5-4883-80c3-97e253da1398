SELECT "image"."Id"
      ,"image"."DefectId"
      ,"defect"."Description" "__Defect"
      ,"property"."Address" "__Property"
      ,"image"."ImageName"
      ,"image"."ImageFileId"
      ,"image"."CreatedAt"
      ,"image"."ModifiedAt"
      ,"image"."Deleted"
FROM public."DefectImage" "image"
INNER JOIN public."PropertyDefect" "defect" ON "image"."DefectId" = "defect"."Id"
INNER JOIN public."Property" "property" ON "defect"."PropertyId" = "property"."Id"
WHERE 1=1
      AND "image"."Deleted" = 'False'
ORDER BY "image"."CreatedAt" DESC;
