import { NextRequest, NextResponse } from "next/server";
import { O_CompanyStrataTitleModel } from "@/app/models/O_CompanyStrataTitle";
import { O_CompanyStrataTitleBusiness } from "../business";

export async function PUT(request: NextRequest) {
    try {
        const dataList: O_CompanyStrataTitleModel[] = await request.json();
        await O_CompanyStrataTitleBusiness.CreateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
