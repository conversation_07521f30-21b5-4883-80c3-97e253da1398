import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { DefectImageModel } from "@/app/models/DefectImage";
import { DefectImageBusiness } from "./business";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Id" or by "DefectId"
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const defectId = params.get("defectId");

        let result;
        if (defectId) {
            result = await DefectImageBusiness.GetByDefectId(defectId);
        } else if (id) {
            result = await DefectImageBusiness.Get(id);
        } else {
            return NextResponse.json({ error: "Missing 'id' or 'defectId' parameter" }, { status: 400 });
        }

        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - CREATE - //
// ---------- //

export async function PUT(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: DefectImageModel[] = await request.json();
            await DefectImageBusiness.CreateList(dataList);
        } else {
            const data: DefectImageModel = await request.json();
            await DefectImageBusiness.Create(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const data: DefectImageModel = await request.json();
        await DefectImageBusiness.Update(data);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const id = params.get("id");

        if (!id) {
            return NextResponse.json({ error: "Missing 'id' parameter" }, { status: 400 });
        }

        await DefectImageBusiness.Delete(id);
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
