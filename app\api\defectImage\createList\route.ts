import { NextRequest, NextResponse } from "next/server";
import { DefectImageModel } from "@/app/models/DefectImage";
import { DefectImageBusiness } from "../business";

export async function PUT(request: NextRequest) {
    try {
        const dataList: DefectImageModel[] = await request.json();
        await DefectImageBusiness.CreateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
