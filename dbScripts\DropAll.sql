-- ==========================================
-- DROP ALL TABLES IN CORRECT ORDER
-- ==========================================
-- This script drops all tables in the correct order to avoid foreign key constraint violations.
-- Tables with foreign key dependencies must be dropped before the tables they reference.

-- Level 3 tables (most dependent)
DROP TABLE IF EXISTS public."DefectImage" CASCADE;

-- Level 2 tables
DROP TABLE IF EXISTS public."PropertyDefect" CASCADE;
DROP TABLE IF EXISTS public."Report" CASCADE;

-- Level 1 tables
DROP TABLE IF EXISTS public."Property" CASCADE;
DROP TABLE IF EXISTS public."Contact" CASCADE;
DROP TABLE IF EXISTS public."UserPersistedData" CASCADE;

-- Level 0 tables (independent) - Option tables
DROP TABLE IF EXISTS public."O_Weather" CASCADE;
DROP TABLE IF EXISTS public."O_Walls" CASCADE;
DROP TABLE IF EXISTS public."O_Roof" CASCADE;
DROP TABLE IF EXISTS public."O_OtherTimberBldgElements" CASCADE;
DROP TABLE IF EXISTS public."O_OtherBuildingElements" CASCADE;
DROP TABLE IF EXISTS public."O_Floor" CASCADE;
DROP TABLE IF EXISTS public."O_Occupied" CASCADE;
DROP TABLE IF EXISTS public."O_Furnished" CASCADE;
DROP TABLE IF EXISTS public."O_Storeys" CASCADE;
DROP TABLE IF EXISTS public."O_Orientation" CASCADE;
DROP TABLE IF EXISTS public."O_NumBedrooms" CASCADE;
DROP TABLE IF EXISTS public."O_CompanyStrataTitle" CASCADE;
DROP TABLE IF EXISTS public."O_BuildingType" CASCADE;

-- Level 0 tables (independent) - Core tables
DROP TABLE IF EXISTS public."GlobalSettings" CASCADE;
DROP TABLE IF EXISTS public."User" CASCADE;
