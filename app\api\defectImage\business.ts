import { sql } from "@vercel/postgres";
import { DefectImageModel } from "@/app/models/DefectImage";

export class DefectImageBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async Get(id: string) {
        return (await sql<DefectImageModel>`
            SELECT "Id",
                   "DefectId",
                   "ImageName",
                   "ImageFileId",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."DefectImage"
            WHERE "Id" = ${id}
              AND "Deleted" = 'False'
        `).rows[0];
    }

    static async GetList() {
        return (await sql<DefectImageModel>`
            SELECT "Id",
                   "DefectId",
                   "ImageName",
                   "ImageFileId",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."DefectImage"
            WHERE "Deleted" = 'False'
            ORDER BY "CreatedAt" DESC
        `).rows;
    }

    static async GetByDefectId(defectId: string) {
        return (await sql<DefectImageModel>`
            SELECT "Id",
                   "DefectId",
                   "ImageName",
                   "ImageFileId",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."DefectImage"
            WHERE "DefectId" = ${defectId}
              AND "Deleted" = 'False'
            ORDER BY "CreatedAt" DESC
        `).rows;
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(data: DefectImageModel) {
        await sql`
            INSERT INTO public."DefectImage"
            (
                "Id",
                "DefectId",
                "ImageName",
                "ImageFileId",
                "CreatedAt"
            )
            VALUES
            (
                ${data.Id},
                ${data.DefectId},
                ${data.ImageName},
                ${data.ImageFileId},
                ${new Date().toUTCString()}
            )
        `;
    }

    static async CreateList(dataList: DefectImageModel[]) {
        for (const data of dataList) {
            await this.Create(data);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(data: DefectImageModel) {
        await sql`
            UPDATE public."DefectImage"
            SET "DefectId" = ${data.DefectId},
                "ImageName" = ${data.ImageName},
                "ImageFileId" = ${data.ImageFileId},
                "ModifiedAt" = ${new Date().toUTCString()},
                "Deleted" = ${data.Deleted}
            WHERE "Id" = ${data.Id}
        `;
    }

    static async UpdateList(dataList: DefectImageModel[]) {
        for (const data of dataList) {
            await this.Update(data);
        }
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id: string) {
        await sql`
            UPDATE public."DefectImage"
            SET "Deleted" = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

    static async DeleteList(ids: string[]) {
        for (const id of ids) {
            await this.Delete(id);
        }
    }
}
