import { NextRequest, NextResponse } from "next/server";
import { PropertyDefectModel } from "@/app/models/PropertyDefect";
import { PropertyDefectBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: PropertyDefectModel[] = await request.json();
        await PropertyDefectBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
