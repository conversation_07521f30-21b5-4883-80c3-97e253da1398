import { NextRequest, NextResponse } from "next/server";
import { DefectCategoryModel } from "@/app/models/DefectCategory";
import { DefectCategoryBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: DefectCategoryModel[] = await request.json();
        await DefectCategoryBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
