import { sql } from "@vercel/postgres";
import { O_OtherBuildingElementsModel } from "@/app/models/O_OtherBuildingElements";

export class O_OtherBuildingElementsBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async Get(code: string) {
        return (await sql<O_OtherBuildingElementsModel>`
            SELECT "Code",
                   "Name",
                   "SortOrder",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."O_OtherBuildingElements"
            WHERE "Code" = ${code}
              AND "Deleted" = 'False'
        `).rows[0];
    }

    static async GetList() {
        return (await sql<O_OtherBuildingElementsModel>`
            SELECT "Code",
                   "Name",
                   "SortOrder",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."O_OtherBuildingElements"
            WHERE "Deleted" = 'False'
            ORDER BY "SortOrder", "Name"
        `).rows;
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(data: O_OtherBuildingElementsModel) {
        await sql`
            INSERT INTO public."O_OtherBuildingElements"
            ("Code", "Name", "SortOrder", "CreatedAt")
            VALUES
            (${data.Code}, ${data.Name}, ${data.SortOrder}, ${new Date().toUTCString()})
        `;
    }

    static async CreateList(dataList: O_OtherBuildingElementsModel[]) {
        for (const data of dataList) {
            await this.Create(data);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(data: O_OtherBuildingElementsModel) {
        await sql`
            UPDATE public."O_OtherBuildingElements"
            SET "Name" = ${data.Name},
                "SortOrder" = ${data.SortOrder},
                "ModifiedAt" = ${new Date().toUTCString()},
                "Deleted" = ${data.Deleted}
            WHERE "Code" = ${data.Code}
        `;
    }

    static async UpdateList(dataList: O_OtherBuildingElementsModel[]) {
        for (const data of dataList) {
            await this.Update(data);
        }
    }

    static async UpdateSortOrder(code: string, sortOrder: number) {
        await sql`
            UPDATE public."O_OtherBuildingElements"
            SET "SortOrder" = ${sortOrder},
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Code" = ${code}
        `;
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(code: string) {
        await sql`
            UPDATE public."O_OtherBuildingElements"
            SET "Deleted" = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Code" = ${code}
        `;
    }

    static async DeleteList(codes: string[]) {
        for (const code of codes) {
            await this.Delete(code);
        }
    }
}
