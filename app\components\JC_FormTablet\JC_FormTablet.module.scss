@import '../../global';

.mainContainer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;

    // Header
    .header {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background-color: $white;
        min-height: 60px;
        gap: 20px;

        .backButton {
            margin-top: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            cursor: pointer;
            transition: opacity 0.2s ease;

            &:hover {
                opacity: 0.7;
            }

            .backButtonIcon {
                width: 20px;
                height: 20px;
            }
        }

        .headerLabel {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
            color: $primaryColor;
        }
    }

    // Content Area
    .contentArea {
        display: flex;
        flex: 1;
        overflow: hidden;

        // Left Pane - Field Tiles
        .leftPane {
            width: 230px;
            min-width: 200px;
            display: flex;
            flex-direction: column;
            background-color: $white;
            overflow: hidden;

            .leftPaneHeader {
                background-color: $primaryColor;
                color: $white;
                padding: 10px 15px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                border-bottom: 1px solid $lightGrey;
            }

            .tileContainer {
                flex: 1;
                overflow-y: auto;
                display: flex;
                flex-direction: column;

                .tile {
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    gap: 2px;
                    justify-content: center;
                    padding: 10px 10px;
                    font-size: 12px;
                    cursor: pointer;
                    box-sizing: border-box;
                    user-select: none;

                    &.headingTile {
                        height: 40px;
                        background-color: $lightGrey;
                        font-weight: bold;
                        color: $primaryColor;
                        cursor: default;
                        margin-top: 15px;
                        justify-content: center;
                        align-items: flex-start;

                        &:first-child {
                            margin-top: 0;
                        }
                    }

                    &.fieldTile {
                        background-color: transparent;
                        color: $offBlack;
                        align-items: flex-start;

                        .fieldLabel {
                            font-weight: 500;
                            line-height: 1.2;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            width: 100%;
                        }

                        .fieldValue {
                            font-size: 10px;
                            color: $secondaryColor;
                            margin-top: 2px;
                            line-height: 1.1;
                            opacity: 0.8;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            width: 100%;
                        }

                        &:hover:not(.selectedTile) {
                            background-color: $grey;
                        }
                    }

                    &.errorTile {
                        background-color: $lightErrorColor;
                    }

                    &.selectedTile {
                        background-color: $secondaryColor;
                        color: $white;
                        cursor: default;

                        .fieldLabel {
                            color: $white;
                        }

                        .fieldValue {
                            color: $white;
                        }
                    }
                }
            }
        }

        // Right Pane - Field Editor
        .rightPane {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: $white;
            overflow: hidden;

            .rightPaneHeader {
                background-color: $secondaryColor;
                color: $white;
                padding: 10px 15px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                border-bottom: 1px solid $lightGrey;
            }

            .fieldEditor {
                flex: 1;
                display: flex;
                flex-direction: column;
                    overflow-y: auto;

                // Add padding only for text input fields (JC_Field component)
                > :not(.optionsList) {
                    padding: 30px;
                }

                .optionsList {
                    display: flex;
                    flex-direction: column;
                    gap: 2px;

                    .optionTile {
                        width: 100%;
                        height: 40px;
                        display: flex;
                        align-items: center;
                        padding: 0 10px;
                        font-size: 12px;
                        cursor: pointer;
                        background-color: transparent;
                        color: $offBlack;
                        box-sizing: border-box;

                        &:hover {
                            background-color: $grey;
                        }

                        &.selectedOption {
                            background-color: $secondaryColor;
                            color: $white;
                        }
                    }
                }
            }

            .emptyState {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                color: $darkGrey;
                font-style: italic;
                font-size: 16px;
            }
        }
    }

    // Footer
    .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background-color: $white;
        border-top: $smallBorderWidth solid $primaryColor;
        min-height: 60px;
        gap: 15px;

        .leftFooterContainer {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .rightFooterContainer {
            display: flex;
            align-items: center;
            gap: 15px;

            .errorMessage {
                color: $errorColor;
                font-weight: bold;
                font-size: 14px;
            }
        }
    }

    // Responsive Design
    @media (max-width: $teenyTinyScreenSize) {
        .contentArea {
            flex-direction: column;

            .leftPane {
                width: 100%;
                min-width: auto;
                height: 300px;
                border-right: none;
                border-bottom: 1px solid $lightGrey;
            }

            .rightPane {
                flex: none;
                height: 400px;
            }
        }
    }
}
