import { NextRequest, NextResponse } from "next/server";
import { DefectImageModel } from "@/app/models/DefectImage";
import { DefectImageBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: DefectImageModel[] = await request.json();
        await DefectImageBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
