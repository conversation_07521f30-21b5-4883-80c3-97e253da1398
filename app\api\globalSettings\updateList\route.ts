import { NextRequest, NextResponse } from "next/server";
import { GlobalSettingsModel } from "@/app/models/GlobalSettings";
import { GlobalSettingsBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: GlobalSettingsModel[] = await request.json();
        await GlobalSettingsBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
