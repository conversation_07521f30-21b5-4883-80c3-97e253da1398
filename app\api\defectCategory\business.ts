import { sql } from "@vercel/postgres";
import { DefectCategoryModel } from "@/app/models/DefectCategory";

export class DefectCategoryBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async Get(code: string) {
        return (await sql<DefectCategoryModel>`
            SELECT "Code",
                   "Name",
                   "SortOrder",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."DefectCategory"
            WHERE "Code" = ${code}
              AND "Deleted" = 'False'
        `).rows[0];
    }

    static async GetList() {
        return (await sql<DefectCategoryModel>`
            SELECT "Code",
                   "Name",
                   "SortOrder",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."DefectCategory"
            WHERE "Deleted" = 'False'
            ORDER BY "SortOrder", "Name"
        `).rows;
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(data: DefectCategoryModel) {
        await sql`
            INSERT INTO public."DefectCategory"
            ("Code", "Name", "SortOrder", "CreatedAt")
            VALUES
            (${data.Code}, ${data.Name}, ${data.SortOrder}, ${new Date().toUTCString()})
        `;
    }

    static async CreateList(dataList: DefectCategoryModel[]) {
        for (const data of dataList) {
            await this.Create(data);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(data: DefectCategoryModel) {
        await sql`
            UPDATE public."DefectCategory"
            SET "Name" = ${data.Name},
                "SortOrder" = ${data.SortOrder},
                "ModifiedAt" = ${new Date().toUTCString()},
                "Deleted" = ${data.Deleted}
            WHERE "Code" = ${data.Code}
        `;
    }

    static async UpdateList(dataList: DefectCategoryModel[]) {
        for (const data of dataList) {
            await this.Update(data);
        }
    }

    static async UpdateSortOrder(code: string, sortOrder: number) {
        await sql`
            UPDATE public."DefectCategory"
            SET "SortOrder" = ${sortOrder},
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Code" = ${code}
        `;
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(code: string) {
        await sql`
            UPDATE public."DefectCategory"
            SET "Deleted" = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Code" = ${code}
        `;
    }

    static async DeleteList(codes: string[]) {
        for (const code of codes) {
            await this.Delete(code);
        }
    }
}
