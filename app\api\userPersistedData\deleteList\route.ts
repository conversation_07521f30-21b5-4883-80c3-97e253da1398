import { NextRequest, NextResponse } from "next/server";
import { UserPersistedDataBusiness } from "../business";

export async function DELETE(request: NextRequest) {
    try {
        const { ids } = await request.json();
        if (!ids || !Array.isArray(ids)) {
            return NextResponse.json({ error: "Missing or invalid 'ids' array in request body" }, { status: 400 });
        }
        await UserPersistedDataBusiness.DeleteList(ids);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
