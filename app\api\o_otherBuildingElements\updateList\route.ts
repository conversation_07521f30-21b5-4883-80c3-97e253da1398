import { NextRequest, NextResponse } from "next/server";
import { O_OtherBuildingElementsModel } from "@/app/models/O_OtherBuildingElements";
import { O_OtherBuildingElementsBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: O_OtherBuildingElementsModel[] = await request.json();
        await O_OtherBuildingElementsBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
