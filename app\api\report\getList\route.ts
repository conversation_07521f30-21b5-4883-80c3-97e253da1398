import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ReportBusiness } from "../business";

export const dynamic = 'force-dynamic';

// Get all Report, optionally filtered by userId
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const userId = params.get("userId");

        let result;
        if (userId) {
            result = await ReportBusiness.GetByUserId(userId);
        } else {
            result = await ReportBusiness.GetList();
        }

        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
