import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";

export class UserModel extends _Base {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "user";
    static apiRoute_getByToken:string = "user/getByToken";
    static async Get(id: string) {
        return await JC_Get<UserModel>(this.apiRoute, { id }, UserModel);
    }
    static async GetList() {
        return await JC_GetList<UserModel>(`${this.apiRoute}/getList`, {}, UserModel);
    }
    static async Create(data: UserModel) {
        return await JC_Put<UserModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: UserModel[]) {
        return await JC_Put<UserModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: UserModel) {
        return await JC_Post<UserModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: UserModel[]) {
        return await JC_Post<UserModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(id: string) {
        return await JC_Delete(this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { ids });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    FirstName: string;
    LastName: string;
    Email: string;
    PasswordHash: string;
    LoginFailedAttempts: number;
    LoginLockoutDate?: Date;
    ChangePasswordToken?: string;
    ChangePasswordTokenDate?: Date;
    Phone?: string;
    IsAdmin: boolean;
    IsWholesale: boolean;
    CompanyName?: string;
    Qualification?: string;
    IsEmailSubscribed: boolean;
    IsDiscountUser: boolean;
    StripeCustomerId?: string;
    IsVerified: boolean;
    VerificationToken?: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<UserModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.FirstName = "";
        this.LastName = "";
        this.Email = "";
        this.PasswordHash = "";
        this.LoginFailedAttempts = 0;
        this.LoginLockoutDate = undefined;
        this.ChangePasswordToken = undefined;
        this.ChangePasswordTokenDate = undefined;
        this.Phone = undefined;
        this.IsAdmin = false;
        this.IsWholesale = false;
        this.CompanyName = undefined;
        this.Qualification = undefined;
        this.IsEmailSubscribed = true;
        this.IsDiscountUser = false;
        this.StripeCustomerId = undefined;
        this.IsVerified = false;
        this.VerificationToken = undefined;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.FirstName} ${this.LastName} | ${this.Email}`;
    }


}
