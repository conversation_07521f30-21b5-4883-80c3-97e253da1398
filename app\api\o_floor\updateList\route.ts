import { NextRequest, NextResponse } from "next/server";
import { O_FloorModel } from "@/app/models/O_Floor";
import { O_FloorBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: O_FloorModel[] = await request.json();
        await O_FloorBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
